/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', 'Aria<PERSON>', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
}

/* Loading Overlay Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: white;
    max-width: 400px;
    padding: 2rem;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.loading-text p {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.loading-progress {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #fbbf24, #f59e0b, #fbbf24);
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
}

.loading-status {
    font-size: 0.9rem;
    opacity: 0.8;
    font-style: italic;
}

/* Simple fade in for content */
.loading-fade-in {
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* Responsive loading screen */
@media (max-width: 768px) {
    .loading-content {
        padding: 1.5rem;
        max-width: 300px;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        margin-bottom: 1.5rem;
    }

    .loading-text h3 {
        font-size: 1.3rem;
    }

    .loading-text p {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Language Toggle Button */
.language-toggle {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 8px 15px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

.language-toggle:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.lang-icon {
    font-size: 16px;
}

.lang-text {
    font-size: 12px;
    font-weight: bold;
}

/* Enhanced Sidebar Controls */
.sidebar-top-controls {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.15);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

.sidebar-auth-section {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.15);
    background: rgba(255, 255, 255, 0.5);
}

.sidebar-language-toggle.enhanced,
.sidebar-login-btn.enhanced,
.sidebar-user-status.enhanced,
.sidebar-logout-btn.enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 25px;
    padding: 12px 20px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
    justify-content: center;
    margin-bottom: 8px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
    border: 2px solid rgba(102, 126, 234, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.sidebar-language-toggle.enhanced:hover,
.sidebar-login-btn.enhanced:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    /* transform removed */
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    border-color: rgba(102, 126, 234, 0.3);
}

.sidebar-language-toggle.enhanced::before,
.sidebar-login-btn.enhanced::before,
.sidebar-user-status.enhanced::before,
.sidebar-logout-btn.enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.sidebar-language-toggle.enhanced:hover::before,
.sidebar-login-btn.enhanced:hover::before {
    left: 100%;
}

/* Special styling for user status button */
.sidebar-user-status.enhanced {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    pointer-events: none;
    opacity: 0.9;
}

/* Special styling for logout button */
.sidebar-logout-btn.enhanced {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    margin-top: 5px;
}

.sidebar-logout-btn.enhanced:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
    /* transform removed */
    box-shadow: 0 8px 25px rgba(244, 67, 54, 0.3);
}

/* Icons styling */
.auth-icon, .lang-icon {
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.auth-text, .lang-text {
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Removed animation for enhanced buttons */

/* Simplified mobile sidebar */
@media (max-width: 768px) {
    /* No animations for better performance */
}

/* Removed slideIn animations */

/* Enhanced button focus states for accessibility */
.sidebar-language-toggle.enhanced:focus,
.sidebar-login-btn.enhanced:focus,
.sidebar-logout-btn.enhanced:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
    transform: scale(1.02);
}

/* Dark mode support for sidebar */
@media (prefers-color-scheme: dark) {
    .sidebar {
        background: linear-gradient(135deg,
            rgba(30, 30, 30, 0.98) 0%,
            rgba(20, 20, 20, 0.95) 100%);
        color: #fff;
    }

    .sidebar-top-controls {
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.15),
            rgba(118, 75, 162, 0.15));
    }

    .sidebar-auth-section {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1),
            rgba(255, 255, 255, 0.05));
    }
}

/* Language-specific styles */
.lang-en {
    font-family: 'Arial', 'Helvetica', sans-serif;
}

.lang-en .hero-title {
    font-family: 'Arial', 'Helvetica', sans-serif;
    letter-spacing: 1px;
}

.lang-en .nav-links {
    direction: ltr;
}

.lang-en .sidebar-nav {
    direction: ltr;
}

.lang-en .hero-title-container {
    flex-direction: row-reverse;
}

/* RTL/LTR specific adjustments */
[dir="ltr"] .hero-title-container {
    flex-direction: row-reverse;
}

[dir="rtl"] .hero-title-container {
    flex-direction: row;
}

/* Hero Title Container with Logo */
.hero-title-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.company-logo-image {
    width: 120px;
    height: 120px;
    object-fit: contain;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    padding: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border: 2px solid rgba(102, 126, 234, 0.2);
    backdrop-filter: blur(10px);
}

/* Removed logo hover animation */

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: all 0.3s ease;
}

/* Enhanced header for mobile */
@media (max-width: 768px) {
    header {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.98) 0%,
            rgba(255, 255, 255, 0.95) 100%);
        backdrop-filter: blur(15px);
        box-shadow:
            0 6px 25px rgba(0, 0, 0, 0.15),
            0 2px 10px rgba(102, 126, 234, 0.1);
        border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    }

    header.scrolled {
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.99) 0%,
            rgba(255, 255, 255, 0.97) 100%);
        box-shadow:
            0 8px 30px rgba(0, 0, 0, 0.2),
            0 4px 15px rgba(102, 126, 234, 0.15);
        border-bottom: 2px solid rgba(102, 126, 234, 0.2);
    }

    nav {
        padding: 0.8rem 0;
    }
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    flex-direction: row-reverse;
}

.logo {
    font-size: 2rem;
    font-weight: bold;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #fbbf24 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    font-family: 'Arial', sans-serif;
    letter-spacing: 1px;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(102, 126, 234, 0.2);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.15);
    transition: all 0.3s ease;
    cursor: pointer;
}

.logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #fbbf24 100%);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.logo:hover {
    background: linear-gradient(135deg, #fbbf24 0%, #764ba2 50%, #667eea 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

.logo:hover::before {
    opacity: 0.1;
}



.login-link {
    background: linear-gradient(45deg, #667eea, #764ba2) !important;
    color: white !important;
    border-radius: 5px !important;
    font-weight: 600 !important;
    box-shadow: 0 3px 15px rgba(102, 126, 234, 0.3) !important;
}

.login-link:hover {
    background: linear-gradient(45deg, #764ba2, #667eea) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.4) !important;
}

.login-link.logged-in {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    pointer-events: none !important;
}

.login-link.logged-in:hover {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    transform: none !important;
}

/* User Menu Dropdown */
.user-menu {
    position: relative;
    display: inline-block;
}

.user-menu-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    color: white !important;
    border-radius: 5px !important;
    font-weight: 600 !important;
    box-shadow: 0 3px 15px rgba(76, 175, 80, 0.3) !important;
    text-decoration: none !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;
    border: none !important;
    font-size: inherit !important;
}

.user-menu-btn:hover {
    background: linear-gradient(45deg, #45a049, #4CAF50) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 20px rgba(76, 175, 80, 0.4) !important;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-dropdown-item {
    display: block;
    padding: 1rem 1.5rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.user-dropdown-item:first-child {
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.user-dropdown-item:last-child {
    border-bottom: none;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.user-dropdown-item:hover {
    background: #f5f5f5;
}

.user-dropdown-item.status {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    font-weight: 600;
    pointer-events: none;
}

.user-dropdown-item.logout {
    color: #f44336;
    font-weight: 500;
}

.user-dropdown-item.logout:hover {
    background: rgba(244, 67, 54, 0.1);
}

.user-dropdown-item.admin-link {
    color: #667eea;
    font-weight: 500;
    cursor: pointer;
}

.user-dropdown-item.admin-link:hover {
    background: rgba(102, 126, 234, 0.1);
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: background 0.2s ease, color 0.2s ease;
    padding: 0.5rem 1rem;
    border-radius: 8px;
}

.nav-links a:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

/* Mobile Menu Button */
.mobile-menu-btn {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
}

.mobile-menu-btn span {
    width: 25px;
    height: 3px;
    background: #667eea;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

/* Simplified Sidebar Styles for Mobile Performance */
.sidebar {
    position: fixed;
    top: 0;
    right: -300px;
    width: 300px;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    box-shadow: -4px 0 15px rgba(0, 0, 0, 0.1);
    transition: right 0.2s ease;
    z-index: 2000;
    padding: 0;
    border-left: 1px solid rgba(102, 126, 234, 0.1);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

.sidebar.active {
    right: 0;
    box-shadow: -12px 0 40px rgba(0, 0, 0, 0.2);
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.sidebar-logo {
    font-size: 1.5rem;
    font-weight: bold;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #fbbf24 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 1px;
    position: relative;
    padding: 0.4rem 0.8rem;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(102, 126, 234, 0.2);
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.15);
    transition: all 0.3s ease;
    cursor: pointer;
}

/* Simplified Sidebar Logo for Mobile Performance */
@media (max-width: 768px) {
    .sidebar-logo {
        font-size: 1.6rem;
        padding: 0.6rem 1rem;
        color: #667eea;
        font-weight: 600;
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 8px;
    }

    .sidebar-logo:hover {
        background: rgba(102, 126, 234, 0.1);
        border-color: rgba(102, 126, 234, 0.3);
    }
}

.sidebar-logo:hover {
    background: linear-gradient(135deg, #fbbf24 0%, #764ba2 50%, #667eea 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    box-shadow: 0 5px 18px rgba(102, 126, 234, 0.25);
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

.close-btn {
    background: none;
    border: none;
    font-size: 2rem;
    color: #667eea;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

/* Enhanced Sidebar Navigation */
.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 1rem 0 0 0;
}

.sidebar-nav li {
    border-bottom: 1px solid rgba(102, 126, 234, 0.08);
    position: relative;
    overflow: hidden;
}

.sidebar-nav li::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #667eea;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.sidebar-nav li:hover::before {
    opacity: 1;
}

.sidebar-nav a {
    display: block;
    padding: 1.2rem 1.5rem;
    color: #333;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.2s ease, color 0.2s ease;
    position: relative;
    z-index: 1;
}

.sidebar-nav a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(102, 126, 234, 0.1);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1;
}

/* Mobile-optimized hover effect */
@media (max-width: 768px) {
    .sidebar-nav a:hover {
        background-color: #667eea;
        color: white;
    }

    .sidebar-nav a::before {
        display: none;
    }
}

/* Desktop hover effect */
@media (min-width: 769px) {
    .sidebar-nav a:hover {
        color: white;
    }

    .sidebar-nav a:hover::before {
        opacity: 1;
    }
}

/* Legacy sidebar login link - hidden in favor of enhanced buttons */
.sidebar-login-link {
    display: none !important;
}

.sidebar-login-link.logged-in {
    display: none !important;
}

.sidebar-login-link.logged-in:hover {
    background: linear-gradient(45deg, #4CAF50, #45a049) !important;
    transform: none !important;
}

/* Sidebar User Menu - Updated for enhanced design */
.sidebar-user-menu {
    margin: 0; /* Remove margin as it's handled by sidebar-auth-section */
}

/* Hide legacy sidebar elements in favor of enhanced buttons */
.sidebar-user-status:not(.enhanced) {
    display: none !important;
}

.sidebar-logout-btn:not(.enhanced) {
    display: none !important;
}

/* Overlay */
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1500;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Main Content Styles */
main {
    margin-top: 100px;
    padding: 2rem 0;
}

.hero-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 3rem 0 0 0;
    margin-bottom: 3rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    overflow: hidden;
}

.hero-title {
    font-size: 3rem;
    color: #fbbf24;
    background: #1e3a8a;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    font-family: 'Arial', sans-serif;
    font-weight: bold;
    letter-spacing: 2px;
    padding: 1.5rem 3rem;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(30, 58, 138, 0.3);
    flex: 1;
    text-align: center;
}

.hero-subtitle {
    font-size: 1.5rem;
    color: #764ba2;
    margin-bottom: 2rem;
    padding: 0 3rem;
}

.company-image-container {
    width: calc(100% - 2rem);
    height: auto;
    min-height: 300px;
    margin: 2rem 1rem 0 1rem;
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4),
                0 8px 25px rgba(0, 0, 0, 0.3),
                0 4px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
}

.company-image-container::before {
    content: '';
    position: absolute;
    top: -40px;
    left: -10px;
    right: -10px;
    height: 40px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), transparent);
    border-radius: 15px;
    pointer-events: none;
    z-index: 2;
}

.company-image-container::after {
    content: '';
    position: absolute;
    bottom: -40px;
    left: -10px;
    right: -10px;
    height: 40px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
    border-radius: 15px;
    pointer-events: none;
    z-index: 2;
}

.company-image {
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    object-position: center;
    display: block;
    border-radius: 15px;
    margin: 0 auto;
}

.image-overlay-top,
.image-overlay-bottom {
    display: none;
}

/* Moving Text Banner */
.moving-text-banner {
    background: linear-gradient(45deg, #667eea, #764ba2);
    padding: 1rem 0;
    overflow: hidden;
    position: relative;
    margin: 2rem 0 0 0;
    border-radius: 25px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
    height: 60px;
    display: flex !important;
    align-items: center;
    visibility: visible !important;
    opacity: 1 !important;
}

.moving-text-banner::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 100px;
    background: linear-gradient(to right, rgba(102, 126, 234, 0.8), transparent);
    z-index: 2;
    border-radius: 25px 0 0 25px;
}

.moving-text-banner::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 100px;
    background: linear-gradient(to left, rgba(118, 75, 162, 0.8), transparent);
    z-index: 2;
    border-radius: 0 25px 25px 0;
}

.moving-text-container {
    width: 100%;
    overflow: hidden;
}

/* Optimized moving text with lighter animation */
.moving-text {
    display: flex !important;
    white-space: nowrap;
    gap: 5rem;
    visibility: visible !important;
    opacity: 1 !important;
    animation: moveText 40s linear infinite;
}

/* Language-specific animations - lighter version */
[dir="rtl"] .moving-text {
    animation: moveTextRTL 40s linear infinite;
}

[dir="ltr"] .moving-text {
    animation: moveTextLTR 40s linear infinite;
}

/* Lighter animation for mobile */
@media (max-width: 768px) {
    .moving-text {
        animation: moveText 50s linear infinite !important;
        gap: 3rem;
    }

    [dir="rtl"] .moving-text {
        animation: moveTextRTL 50s linear infinite !important;
    }

    [dir="ltr"] .moving-text {
        animation: moveTextLTR 50s linear infinite !important;
    }
}

/* Ensure animation works on larger screens */
@media (min-width: 769px) {
    .moving-text {
        animation: moveText 40s linear infinite !important;
    }

    [dir="rtl"] .moving-text {
        animation: moveTextRTL 40s linear infinite !important;
    }

    [dir="ltr"] .moving-text {
        animation: moveTextLTR 40s linear infinite !important;
    }
}

.moving-text span {
    color: white;
    font-size: 1.3rem;
    font-weight: 700;
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.4);
    letter-spacing: 1.2px;
    display: inline-block;
    min-width: max-content;
    position: relative;
    z-index: 3;
}

/* Animation for Arabic (RTL) - starts from left */
@keyframes moveTextRTL {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Animation for English (LTR) - starts from right */
@keyframes moveTextLTR {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Default animation (Arabic) */
@keyframes moveText {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}



/* About Section Styles */
.about-section {
    padding: 2rem 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    margin: 1rem 0;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.about-title {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 2rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    padding: 0 2rem;
}

.about-description {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #555;
    margin: 0;
    text-align: justify;
    text-align-last: center;
}

.about-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 0;
    color: #666;
}

.about-loading .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.about-loading p {
    font-size: 1.1rem;
    margin: 0;
    color: #888;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Gallery Section Styles */
.gallery-section {
    margin-top: 2rem;
}

/* Featured Badge */
.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
    z-index: 2;
}

/* Category Badge */
.category-badge {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

/* Gallery Item Meta */
.gallery-item-meta {
    margin: 0.5rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* No Gallery Message */
.no-gallery-message {
    text-align: center;
    padding: 3rem;
    color: #666;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    margin: 2rem 0;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
    padding: 0 1rem;
}

.gallery-item {
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border: 2px solid #f0f0f0;
    position: relative;
}

.gallery-item:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.gallery-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    object-position: center;
    display: block;
    transition: opacity 0.2s ease;
}

.gallery-item:hover .gallery-image {
    opacity: 0.9;
}

/* تنسيق النص المتراكب على الصور */
.gallery-item-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.9));
    color: white;
    padding: 1.5rem 1rem 1rem;
    transform: translateY(100%);
    transition: transform 0.4s ease;
    z-index: 3;
}

.gallery-item:hover .gallery-item-overlay {
    transform: translateY(0);
}

.gallery-item-overlay h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: #FFD700;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.gallery-item-overlay p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
    color: #f0f0f0;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* Main Gallery Section Styles */
.gallery-main-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    margin: 3rem 0;
    border-radius: 20px;
}

.gallery-main-title {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gallery-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Simplified Gallery Content for mobile performance */
.gallery-content {
    background: rgba(255, 255, 255, 0.98);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
    flex-wrap: wrap;
    gap: 1rem;
}

.current-category-title {
    font-size: 1.5rem;
    color: #333;
    margin: 0;
    font-weight: 600;
}

.gallery-controls {
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-pagination {
    display: flex;
    align-items: center;
}

.page-info {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
    background: rgba(102, 126, 234, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.page-info span {
    color: #667eea;
    font-weight: 700;
}



/* Gallery Main Grid */
.gallery-main-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    transition: all 0.3s ease;
    min-height: 600px;
}

.gallery-main-grid.list-view {
    grid-template-columns: 1fr;
    gap: 2rem;
}

.gallery-main-item {
    display: flex;
    flex-direction: column;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border: 2px solid #f0f2f5;
    min-height: auto;
    height: fit-content;
}

.gallery-main-item:hover {
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.2);
    border-color: #667eea;
}

.gallery-image-container {
    position: relative;
    overflow: hidden;
    background: #f8f9fa;
    height: 240px;
    border-radius: 15px 15px 0 0;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    will-change: transform;
}

.gallery-main-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: all 0.3s ease;
    filter: brightness(1) contrast(1.08) saturate(1.05);
    display: block;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: high-quality;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
}

.gallery-main-item:hover .gallery-main-image {
    opacity: 0.95;
}

.gallery-item-info {
    padding: 1.5rem;
    background: white;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.gallery-item-info h4 {
    margin: 0 0 0.8rem 0;
    font-size: 1.3rem;
    font-weight: 700;
    line-height: 1.3;
    color: #333;
    text-align: right;
}

.gallery-item-info p {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    line-height: 1.6;
    color: #666;
    text-align: right;
    flex-grow: 1;
}

/* Gallery Loading */
.gallery-loading {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.gallery-loading .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}



/* Branches Section Styles */
.branches-section {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.branches-title {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 2rem;
    text-align: center;
}

.branches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.branch-card {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.branch-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.branch-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.branch-card a {
    color: white;
    text-decoration: none;
    font-weight: bold;
    display: inline-block;
    margin-top: 1rem;
    padding: 0.5rem 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    transition: all 0.3s ease;
}

.branch-card a:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Section Separator */
.section-separator {
    height: 80px;
    background: linear-gradient(45deg, transparent 0%, rgba(102, 126, 234, 0.1) 50%, transparent 100%);
    margin: 3rem 0;
    position: relative;
}

.section-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
}

/* Contact Section Styles */
.contact-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 4rem 3rem;
    margin-bottom: 3rem;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.contact-title {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 2rem;
    text-align: center;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.contact-info h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding: 1.5rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.contact-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.contact-icon {
    font-size: 2rem;
    margin-left: 1rem;
    flex-shrink: 0;
}

.contact-details {
    flex: 1;
}

.contact-details strong {
    color: #667eea;
    display: block;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.contact-details span {
    display: block;
    color: #555;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.contact-action-btn {
    display: inline-block;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-top: 0.5rem;
}

.contact-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.contact-form-container h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.contact-form .form-group {
    margin-bottom: 1.5rem;
}

.contact-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #667eea;
}

.contact-submit-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.contact-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.contact-submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Footer Styles */
footer {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Hide desktop navigation and show mobile menu button */
    .desktop-nav {
        display: none;
    }

    .mobile-menu-btn {
        display: flex;
    }

    /* Simplified Logo for Mobile Performance */
    .logo {
        font-size: 1.8rem;
        padding: 0.8rem 1.2rem;
        color: #667eea;
        font-weight: 700;
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.9);
    }

    .logo:hover {
        background: rgba(102, 126, 234, 0.1);
        border-color: rgba(102, 126, 234, 0.3);
    }

    .hero-title {
        font-size: 2rem;
        padding: 1rem 1.5rem;
        color: #fbbf24;
        background: #1e3a8a;
        border-radius: 12px;
        box-shadow: 0 6px 20px rgba(30, 58, 138, 0.3);
    }

    .company-image-container {
        width: calc(100% - 4rem);
        height: auto;
        min-height: 220px;
        margin: 2rem 2rem 2rem 2rem;
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.35),
                    0 6px 20px rgba(0, 0, 0, 0.25),
                    0 3px 12px rgba(0, 0, 0, 0.15);
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
    }

    .company-image-container::before {
        display: none;
    }

    .company-image-container::after {
        display: none;
    }

    .hero-subtitle {
        font-size: 1.2rem;
        padding: 0 1.5rem;
    }

    .branches-grid {
        grid-template-columns: 1fr;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
        margin-top: 2rem;
    }

    .gallery-item {
        background: white;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        overflow: hidden;
        transition: all 0.3s ease;
        border: 2px solid #f0f0f0;
    }

    .gallery-item:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
        border-color: #667eea;
    }

    .gallery-image {
        height: 220px;
        width: 100%;
        object-fit: cover;
        object-position: center;
        transition: all 0.3s ease;
        border-radius: 0;
    }

    .gallery-item:hover .gallery-image {
        transform: scale(1.05);
        filter: brightness(1.1) contrast(1.1);
    }

    /* Gallery Main Section Responsive */
    .gallery-main-section {
        padding: 2rem 0;
        margin: 2rem 0;
    }

    .gallery-main-title {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .gallery-container {
        padding: 0 1rem;
    }

    .gallery-content {
        padding: 1.5rem;
    }

    .moving-text span {
        font-size: 1.1rem;
        letter-spacing: 1px;
    }

    .moving-text-banner {
        padding: 0.8rem 0;
        margin: 0.8rem 1rem;
        border-radius: 20px;
        height: 50px;
    }

    .moving-text-banner::before,
    .moving-text-banner::after {
        width: 80px;
    }

    .gallery-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .current-category-title {
        text-align: center;
        font-size: 1.3rem;
    }

    .gallery-controls {
        justify-content: center;
    }

    .gallery-main-grid {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
        gap: 1.3rem;
    }

    .gallery-main-image {
        height: 200px;
    }

    .gallery-image-container {
        height: 200px;
    }

    .gallery-item-info {
        padding: 1.2rem;
    }

    .gallery-item-info h4 {
        font-size: 1.1rem;
    }

    .gallery-item-info p {
        font-size: 0.9rem;
    }

    .gallery-item-actions {
        padding: 0.7rem 1.2rem;
        margin-top: 1rem;
    }

    .view-text {
        font-size: 0.85rem;
    }

    .gallery-categories {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .gallery-category {
        padding: 0.8rem;
        text-align: center;
        flex-direction: column;
        gap: 0.3rem;
    }

    .category-icon {
        margin: 0;
        font-size: 1.5rem;
    }

    .category-name {
        font-size: 0.8rem;
    }

    .gallery-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        padding: 1rem;
    }

    .logo {
        font-size: 1.6rem;
        padding: 0.7rem 1rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 40%, #fbbf24 80%, #f093fb 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
        border: 1.5px solid rgba(102, 126, 234, 0.25);
        transform: scale(1.02);
    }

    /* Hero Title Container for Medium Screens */
    .hero-title-container {
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        backdrop-filter: blur(15px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    }

    .company-logo-image {
        width: 110px;
        height: 110px;
        padding: 8px;
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
        border: 3px solid rgba(102, 126, 234, 0.3);
        transform: scale(1.05);
        /* animation removed */
    }

    .company-logo-image:hover {
        transform: scale(1.15) rotate(5deg);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        border-color: rgba(102, 126, 234, 0.5);
    }



    .branches-title,
    .contact-title,
    .about-title {
        font-size: 2rem;
    }

    .about-section {
        padding: 1.5rem 0;
        margin: 0.8rem 0;
    }

    .about-description {
        font-size: 1.1rem;
        text-align: center;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-section {
        padding: 2rem 1.5rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .contact-icon {
        margin: 0 0 1rem 0;
    }

    .contact-details strong {
        font-size: 1rem;
    }

    .section-separator {
        height: 60px;
        margin: 2rem 0;
    }

    /* Enhanced sidebar for smaller screens */
    .sidebar {
        width: 280px;
        right: -280px;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.98) 0%,
            rgba(255, 255, 255, 0.95) 100%);
        backdrop-filter: blur(15px);
        box-shadow: -8px 0 30px rgba(0, 0, 0, 0.15);
    }

    /* Enhanced mobile sidebar controls */
    .sidebar-top-controls {
        padding: 1rem;
        background: linear-gradient(135deg,
            rgba(102, 126, 234, 0.08),
            rgba(118, 75, 162, 0.08));
    }

    .sidebar-auth-section {
        padding: 1rem;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.7),
            rgba(255, 255, 255, 0.5));
    }

    /* Enhanced buttons for mobile */
    .sidebar-language-toggle.enhanced,
    .sidebar-login-btn.enhanced,
    .sidebar-user-status.enhanced,
    .sidebar-logout-btn.enhanced {
        padding: 14px 18px;
        font-size: 15px;
        border-radius: 20px;
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
        /* animation removed */
    }

    .sidebar-language-toggle.enhanced:hover,
    .sidebar-login-btn.enhanced:hover {
        /* transform removed */
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.35);
    }
}

@media (max-width: 480px) {
    /* Simplified Logo for Small Screens */
    .logo {
        font-size: 1.4rem;
        padding: 0.6rem 0.9rem;
        color: #667eea;
        font-weight: 700;
        border: 1px solid rgba(102, 126, 234, 0.2);
        border-radius: 6px;
        background: rgba(255, 255, 255, 0.95);
    }

    /* Hero Title Container for Small Screens */
    .hero-title-container {
        flex-direction: column;
        gap: 1.2rem;
        margin-bottom: 1.5rem;
        padding: 1.2rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.05));
        border-radius: 25px;
        backdrop-filter: blur(20px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .company-logo-image {
        width: 90px;
        height: 90px;
        padding: 8px;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.92));
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.25);
        border: 3px solid rgba(102, 126, 234, 0.4);
        transform: scale(1.1);
        /* animation removed */
        position: relative;
        z-index: 10;
    }

    .company-logo-image:hover {
        transform: scale(1.2) rotate(8deg);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.35);
        border-color: rgba(102, 126, 234, 0.6);
    }

    .company-logo-image::before {
        content: '';
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        background: linear-gradient(45deg, #667eea, #764ba2, #fbbf24, #f093fb);
        border-radius: 15px;
        z-index: -1;
        opacity: 0.3;
        /* animation removed */
    }

    .hero-title {
        font-size: 1.5rem;
        padding: 0.8rem 1rem;
        color: #fbbf24;
        background: #1e3a8a;
        border-radius: 10px;
        box-shadow: 0 4px 15px rgba(30, 58, 138, 0.3);
    }

    .company-image-container {
        width: calc(100% - 3rem);
        height: auto;
        min-height: 180px;
        margin: 2rem 1.5rem 2rem 1.5rem;
        box-shadow: 0 10px 35px rgba(0, 0, 0, 0.3),
                    0 5px 18px rgba(0, 0, 0, 0.2),
                    0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
    }

    .company-image-container::before {
        display: none;
    }

    .company-image-container::after {
        display: none;
    }

    .company-image {
        width: auto;
        height: auto;
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        object-position: center;
        filter: brightness(1.05) contrast(1.1);
        border-radius: 15px;
        display: block;
        margin: 0 auto;
    }

    .hero-subtitle {
        font-size: 1rem;
        padding: 0 1rem;
    }





    .gallery-grid {
        gap: 2rem;
        padding: 0 0.5rem;
        margin-top: 2.5rem;
    }

    .gallery-item {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        overflow: hidden;
        transition: all 0.4s ease;
        border: 3px solid #f5f5f5;
        margin-bottom: 1rem;
    }

    .gallery-item:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        border-color: #667eea;
    }

    .gallery-image {
        height: 200px;
        width: 100%;
        object-fit: cover;
        object-position: center;
        transition: all 0.4s ease;
        border-radius: 0;
        filter: brightness(1.05) contrast(1.05);
    }

    .gallery-item:hover .gallery-image {
        transform: scale(1.08);
        filter: brightness(1.15) contrast(1.15) saturate(1.1);
    }

    .gallery-section {
        margin-top: 2rem;
        padding: 0 0.5rem;
    }

    /* إضافة تأثير لمعان للسيارات في الموبايل */
    .gallery-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
        z-index: 1;
        pointer-events: none;
    }

    .gallery-item:hover::before {
        left: 100%;
    }

    /* تحسين النص تحت الصور */
    .gallery-item::after {
        content: attr(data-title);
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        color: white;
        padding: 1rem;
        font-weight: 600;
        text-align: center;
        transform: translateY(100%);
        transition: transform 0.3s ease;
        z-index: 2;
    }

    .gallery-item:hover::after {
        transform: translateY(0);
    }

    /* تحسين النص المتراكب في الموبايل */
    .gallery-item-overlay {
        padding: 1rem 0.8rem 0.8rem;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.95));
    }

    .gallery-item-overlay h3 {
        font-size: 1rem;
        margin-bottom: 0.3rem;
        color: #FFD700;
        text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9);
    }

    .gallery-item-overlay p {
        font-size: 0.8rem;
        line-height: 1.3;
        color: #f5f5f5;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9);
    }

    /* Gallery Main Section for Small Screens */
    .gallery-main-section {
        padding: 1.5rem 0;
        margin: 1.5rem 0;
        border-radius: 15px;
    }

    .gallery-main-title {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
    }

    .gallery-container {
        padding: 0 0.5rem;
        gap: 1rem;
    }

    .gallery-sidebar {
        padding: 1rem;
    }

    .gallery-content {
        padding: 1rem;
    }

    .gallery-main-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .gallery-main-image {
        height: 180px;
    }

    .gallery-image-container {
        height: 180px;
    }

    .gallery-item-info {
        padding: 1rem;
    }

    .gallery-item-info h4 {
        font-size: 1rem;
    }

    .gallery-item-info p {
        font-size: 0.85rem;
    }

    .gallery-item-actions {
        padding: 0.6rem 1rem;
        margin-top: 0.8rem;
    }

    .view-icon {
        font-size: 1.1rem;
    }

    .view-text {
        font-size: 0.8rem;
    }



    .pagination-controls {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .pagination-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.9rem;
    }

    .page-number {
        width: 35px;
        height: 35px;
    }

    .moving-text span {
        font-size: 1rem;
        letter-spacing: 0.8px;
    }

    .moving-text-banner {
        padding: 0.6rem 0;
        margin: 0.8rem 0.5rem;
        border-radius: 15px;
        height: 40px;
    }

    .moving-text-banner::before,
    .moving-text-banner::after {
        width: 60px;
    }

    .moving-text {
        animation: moveText 25s linear infinite;
        gap: 3rem;
    }

    /* Language-specific animations for mobile */
    [dir="rtl"] .moving-text {
        animation: moveTextRTL 25s linear infinite;
    }

    [dir="ltr"] .moving-text {
        animation: moveTextLTR 25s linear infinite;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .gallery-header {
        margin-bottom: 1rem;
        padding-bottom: 0.8rem;
    }

    .current-category-title {
        font-size: 1.1rem;
    }

    .gallery-control-btn {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    /* Image Modal Responsive */
    .image-modal-content {
        max-width: 95%;
        max-height: 95%;
    }

    .image-modal-img {
        max-height: 60vh;
    }

    .image-modal-info {
        padding: 1.5rem;
    }

    .image-modal-info h3 {
        font-size: 1.3rem;
    }

    .image-modal-info p {
        font-size: 0.9rem;
    }

    .image-modal-close {
        top: 10px;
        right: 15px;
        width: 35px;
        height: 35px;
        font-size: 1.5rem;
    }

    /* Enhanced sidebar for very small screens */
    .sidebar {
        width: 260px;
        right: -260px;
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.99) 0%,
            rgba(255, 255, 255, 0.96) 100%);
        backdrop-filter: blur(20px);
        box-shadow: -10px 0 35px rgba(0, 0, 0, 0.2);
    }

    .sidebar-header {
        padding: 0.8rem;
    }

    .sidebar-nav a {
        padding: 0.9rem;
        font-size: 15px;
    }

    /* Compact enhanced buttons for very small screens */
    .sidebar-top-controls {
        padding: 0.8rem;
    }

    .sidebar-auth-section {
        padding: 0.8rem;
    }

    .sidebar-language-toggle.enhanced,
    .sidebar-login-btn.enhanced,
    .sidebar-user-status.enhanced,
    .sidebar-logout-btn.enhanced {
        padding: 12px 16px;
        font-size: 14px;
        border-radius: 18px;
        margin-bottom: 6px;
    }

    .auth-icon, .lang-icon {
        font-size: 16px;
    }

    .auth-text, .lang-text {
        font-size: 13px;
    }


}

/* Dynamic Content Animations */
.content-updating {
    transition: all 0.3s ease;
    transform: scale(1.02);
    box-shadow: 0 0 15px rgba(102, 126, 234, 0.4);
}

.firebase-image {
    animation: fadeInUp 0.5s ease-out;
}

/* Simplified animations for mobile performance */
@keyframes fadeInUp {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Remove heavy animations for mobile */
@media (max-width: 768px) {
    .gallery-main-item,
    .gallery-category,
    .stat-number {
        animation: none;
    }
}

/* Smooth scrolling for gallery navigation */
html {
    scroll-behavior: smooth;
}

/* Gallery section scroll padding */
#gallery {
    scroll-margin-top: 80px;
}

/* Image Modal Styles */
.image-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 3000;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-modal.active {
    opacity: 1;
}

.image-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.image-modal.active .image-modal-content {
    transform: scale(1);
}

.image-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 2rem;
    color: white;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    cursor: pointer;
    z-index: 3001;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.image-modal-close:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.image-modal-img {
    width: 100%;
    max-height: 70vh;
    object-fit: contain;
    display: block;
}

.image-modal-info {
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.image-modal-info h3 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    color: #333;
    font-weight: 600;
}

.image-modal-info p {
    margin: 0;
    color: #666;
    font-size: 1rem;
    line-height: 1.6;
}

/* Gallery Item Actions */
.gallery-item-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    margin-top: auto;
    padding: 0.8rem 1.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 8px;
    border: none;
    transition: all 0.3s ease;
    cursor: pointer;
}

.gallery-item-actions:hover {
    background: linear-gradient(45deg, #764ba2, #667eea);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.view-icon {
    font-size: 1.2rem;
    color: white;
}

.view-text {
    font-size: 0.95rem;
    font-weight: 600;
    color: white;
}

.gallery-main-item {
    cursor: pointer;
}

/* Pagination Controls */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 3rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.pagination-btn:hover {
    background: #667eea;
    opacity: 0.9;
}

.pagination-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.pagination-numbers {
    display: flex;
    gap: 0.5rem;
}

.page-number {
    width: 40px;
    height: 40px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-number:hover {
    background: rgba(102, 126, 234, 0.1);
}

.page-number.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
}

/* Gallery Item Hover Effects */
.gallery-main-item:hover .gallery-item-info h4 {
    color: #667eea;
    transform: translateY(-1px);
}

.gallery-main-item:hover .gallery-item-info p {
    color: #555;
}

.gallery-main-item:hover .gallery-item-actions {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* Image Modal Navigation */
.image-modal-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    pointer-events: none;
    z-index: 3002;
}

.modal-nav-btn {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    font-size: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-nav-btn:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.modal-nav-btn.prev {
    margin-right: auto;
}

.modal-nav-btn.next {
    margin-left: auto;
}

/* Modal Counter */
.modal-counter {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 20px;
    display: inline-block;
}

#modalCounter {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 600;
}

/* Modal Image Transitions */
.image-modal-img {
    transition: opacity 0.3s ease;
}

/* Mobile Modal Navigation */
@media (max-width: 768px) {
    .image-modal-nav {
        padding: 0 10px;
    }

    .modal-nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1.5rem;
    }

    .image-modal-close {
        top: 10px;
        right: 10px;
        width: 30px;
        height: 30px;
        font-size: 1.2rem;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Removed logo animations */

@keyframes logoRing {
    0% {
        transform: rotate(0deg) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: rotate(180deg) scale(1.05);
        opacity: 0.5;
    }
    100% {
        transform: rotate(360deg) scale(1);
        opacity: 0.3;
    }
}

/* Logo Pulse Effect for Mobile */
@keyframes logoPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* Mobile Logo Enhancements */
@media (max-width: 768px) {
    /* Sticky Header Logo Enhancement */
    header.scrolled .logo {
        font-size: 1.3rem;
        padding: 0.5rem 0.8rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 40%, #fbbf24 80%, #f093fb 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        border: 1.5px solid rgba(102, 126, 234, 0.4);
        /* animation removed */
    }

    /* Logo Focus State for Mobile */
    .logo:focus,
    .logo:active {
        outline: none;
        transform: scale(1.12);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.5);
        border-color: rgba(102, 126, 234, 0.6);
    }

    /* Company Logo Enhanced Visibility */
    .company-logo-image {
        position: relative;
        z-index: 20;
    }

    .company-logo-image::after {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        z-index: -1;
        /* animation removed */
    }
}

@keyframes logoAura {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.6;
    }
}

/* Additional Mobile Logo Enhancements */
@media (max-width: 480px) {
    /* Special mobile-only logo effects */
    .hero-title-container::before {
        content: '';
        position: absolute;
        top: -20px;
        left: -20px;
        right: -20px;
        bottom: -20px;
        background: linear-gradient(45deg,
            rgba(102, 126, 234, 0.1),
            rgba(118, 75, 162, 0.1),
            rgba(251, 191, 36, 0.1),
            rgba(240, 147, 251, 0.1));
        border-radius: 35px;
        z-index: -1;
        /* animation removed */
    }

    .hero-title-container {
        position: relative;
        overflow: visible;
    }

    /* Enhanced company logo for very small screens */
    .company-logo-image {
        border-radius: 20px;
        box-shadow:
            0 15px 40px rgba(0, 0, 0, 0.3),
            0 5px 15px rgba(102, 126, 234, 0.4),
            inset 0 2px 5px rgba(255, 255, 255, 0.3);
    }

    /* Logo text shadow for better visibility */
    .logo {
        text-shadow:
            0 2px 4px rgba(0, 0, 0, 0.3),
            0 0 10px rgba(102, 126, 234, 0.5);
    }

    /* Sidebar logo enhancement for small screens */
    .sidebar-logo {
        font-size: 1.4rem;
        text-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
    }
}

@keyframes logoBackground {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        opacity: 0.3;
    }
    33% {
        transform: rotate(120deg) scale(1.05);
        opacity: 0.5;
    }
    66% {
        transform: rotate(240deg) scale(1.02);
        opacity: 0.4;
    }
}

/* Logo loading animation */
.logo-loading {
    /* animation removed */
}

@keyframes logoLoad {
    0% {
        opacity: 0;
        transform: scale(0.5) rotate(-180deg);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2) rotate(-90deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

/* Ultra-small screens (320px and below) */
@media (max-width: 320px) {
    .logo {
        font-size: 1.2rem;
        padding: 0.5rem 0.7rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 20%, #fbbf24 40%, #f093fb 60%, #667eea 80%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        /* animation removed */
    }

    .company-logo-image {
        width: 75px;
        height: 75px;
        padding: 6px;
        /* animation removed */
    }

    .hero-title-container {
        gap: 1rem;
        padding: 1rem;
        margin-bottom: 1.2rem;
    }

    .sidebar-logo {
        font-size: 1.2rem;
        padding: 0.4rem 0.7rem;
    }
}

/* Landscape mobile orientation */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-title-container {
        flex-direction: row;
        gap: 2rem;
        padding: 0.8rem 1.5rem;
    }

    .company-logo-image {
        width: 80px;
        height: 80px;
    }

    .logo {
        font-size: 1.3rem;
    }
}

/* High DPI screens (Retina) */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo {
        text-shadow:
            0 1px 2px rgba(0, 0, 0, 0.3),
            0 0 5px rgba(102, 126, 234, 0.5);
    }

    .company-logo-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Logo accessibility and performance optimizations */
.logo, .company-logo-image, .sidebar-logo {
    will-change: transform, box-shadow;
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Mobile performance optimizations */
@media (max-width: 768px) {
    /* Disable heavy effects on mobile for better performance */
    * {
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
    }

    .logo, .company-logo-image, .sidebar-logo,
    .gallery-item, .gallery-main-item,
    .sidebar-nav a, .nav-links a {
        transform: none !important;
        animation: none !important;
    }

    /* Keep moving text animation but exclude it from the above rule */
    .moving-text {
        animation: moveText 50s linear infinite !important;
    }

    [dir="rtl"] .moving-text {
        animation: moveTextRTL 50s linear infinite !important;
    }

    [dir="ltr"] .moving-text {
        animation: moveTextLTR 50s linear infinite !important;
    }

    .logo:hover, .company-logo-image:hover,
    .gallery-item:hover, .gallery-main-item:hover {
        transform: none !important;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    .logo, .company-logo-image, .sidebar-logo {
        animation: none !important;
        transition: none !important;
    }

    .logo:hover, .company-logo-image:hover {
        transform: none !important;
    }
}

/* Dark mode support for logos */
@media (prefers-color-scheme: dark) {
    .logo {
        background: linear-gradient(135deg, #8b9cf7 0%, #9d7bc8 30%, #fdd835 70%, #f8bbd9 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 2px 8px rgba(139, 156, 247, 0.4);
    }

    .company-logo-image {
        background: rgba(255, 255, 255, 0.98);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4);
        border-color: rgba(139, 156, 247, 0.4);
    }
}

/* Print styles - hide animations */
@media print {
    .logo, .company-logo-image, .sidebar-logo {
        animation: none !important;
        transform: none !important;
        box-shadow: none !important;
    }
}

/* Real-time update indicator */
.update-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    z-index: 1001;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.update-indicator.show {
    opacity: 1;
    transform: translateX(0);
}

/* Loading states */
.content-loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* No data messages */
.no-data-message {
    text-align: center;
    padding: 3rem 2rem;
    background: rgba(102, 126, 234, 0.05);
    border: 2px dashed rgba(102, 126, 234, 0.2);
    border-radius: 15px;
    margin: 2rem 0;
    color: #666;
    font-style: italic;
    transition: all 0.3s ease;
}

.no-data-message:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
}

.no-data-message p {
    margin: 0;
    font-size: 1.1rem;
    line-height: 1.6;
}

.no-data-message.hidden {
    display: none;
}

/* Force visible no-data messages to stay visible */
.no-data-message[data-force-visible="true"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.no-data-message[data-force-visible="true"].hidden {
    display: block !important;
}

/* Loading Animation for Text */
.loading-dots {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8em;
    animation: pulse 1.5s ease-in-out infinite;
}

.loading-dots span {
    animation: dots 1.5s infinite;
    opacity: 0;
}

.loading-dots span:nth-child(1) {
    animation-delay: 0s;
}

.loading-dots span:nth-child(2) {
    animation-delay: 0.3s;
}

.loading-dots span:nth-child(3) {
    animation-delay: 0.6s;
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

@keyframes dots {
    0%, 20% {
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}

/* Hide loading dots when text is loaded */
.hero-title:not(:empty) .loading-dots,
.hero-subtitle:not(:empty) .loading-dots {
    display: none;
}
